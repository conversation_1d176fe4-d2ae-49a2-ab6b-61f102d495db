using System.Data;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace YdControl
{
    public partial class ComboYp : MyDtComobo
    {
        public ComboYp()
        {
            InitializeComponent();
        }

        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            BLL.BllZd_Yp2 _bllZd_Yp2 = new BllZd_Yp2();
            this.DataView = _bllZd_Yp2.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("Yp_Name", "药品名称", 200, "左");
            this.Init_Colum("Yp_Jc", "简称", 80, "左");
            this.Init_Colum("Xl_Code", "编码", 80, "左");
            this.Init_Colum("Yp_Zjgg", "规格", 120, "左");
            this.Init_Colum("Yp_Zjdw", "单位", 60, "左");
            this.Init_Colum("Yp_Scqy", "生产企业", 150, "左");
            this.DisplayMember = "Yp_Name";
            this.ValueMember = "Xl_Code";
            int width = 450;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "Xl_Code+isnull(Yp_Jc,'')+Yp_Name+isnull(Yp_Pzwh,'')";
        }
    }
}
